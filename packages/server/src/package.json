{"name": "src", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "vite build", "start": "vite", "start:local": "LOCAL_DEV=1 vite --config vite.local.config.ts", "preview": "vite preview", "lint:p": "npx prettier ./src/** --write", "lint:es": "npx eslint src", "lint:fix": "npx eslint src --fix", "lint:s": "stylelint src/**/*.css --fix", "lint:ts": "tsc -p ./tsconfig.json --skipL<PERSON><PERSON><PERSON><PERSON>", "lint": "npm run lint:prettier && npm run lint:es", "devcert": "node ./certs/devcert.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@growthbook/growthbook": "^1.6.1", "@growthbook/growthbook-react": "^1.6.1", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^6.1.6", "@mui/lab": "6.0.0-beta.14", "@mui/material": "^6.1.6", "@mui/x-date-pickers": "^7.23.1", "@tanstack/react-query": "^5.59.20", "@tanstack/react-router": "^1.85.5", "@tiptap/core": "^2.10.3", "@tiptap/extension-bold": "^2.10.3", "@tiptap/extension-bullet-list": "^2.10.3", "@tiptap/extension-color": "^2.10.3", "@tiptap/extension-hard-break": "^2.10.3", "@tiptap/extension-heading": "^2.10.3", "@tiptap/extension-highlight": "^2.10.3", "@tiptap/extension-image": "^2.10.3", "@tiptap/extension-italic": "^2.10.3", "@tiptap/extension-link": "^2.10.3", "@tiptap/extension-list-item": "^2.10.3", "@tiptap/extension-ordered-list": "^2.10.3", "@tiptap/extension-strike": "^2.10.3", "@tiptap/extension-subscript": "^2.10.3", "@tiptap/extension-superscript": "^2.10.3", "@tiptap/extension-table": "^2.10.3", "@tiptap/extension-table-cell": "^2.10.3", "@tiptap/extension-table-header": "^2.10.3", "@tiptap/extension-table-row": "^2.10.3", "@tiptap/extension-text-align": "^2.10.3", "@tiptap/extension-text-style": "^2.10.3", "@tiptap/extension-underline": "^2.10.3", "@tiptap/pm": "^2.10.3", "@tiptap/react": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "@trpc/client": "11.0.0-rc.621", "@trpc/react-query": "11.0.0-rc.621", "@trpc/server": "11.0.0-rc.621", "@tsconfig/vite-react": "^7.0.0", "@types/lodash": "^4.17.13", "@types/node": "^24.3.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-gtm-module": "^2.0.4", "@vis.gl/react-google-maps": "^1.5.5", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.20", "classnames": "^2.5.1", "dayjs": "^1.11.13", "debounce": "^2.2.0", "dotenv": "^16.4.7", "embla-carousel": "^8.6.0", "embla-carousel-react": "^8.6.0", "firebase": "^12.1.0", "fuse.js": "^7.0.0", "globals": "^15.12.0", "is-mobile": "^5.0.0", "lodash": "^4.17.21", "mui-tiptap": "^1.17.0", "postcss": "^8.4.48", "postcss-load-config": "^6.0.1", "postcss-modules-values": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.53.2", "typescript": "^5.9.2", "use-changed-props": "^0.1.0", "uuid": "^12.0.0", "vite": "^7.1.3", "vite-plugin-svgr": "^4.3.0", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/compat": "^1.2.2", "@eslint/js": "^9.14.0", "@hookform/devtools": "^4.3.3", "@tanstack/router-devtools": "^1.79.0", "@welldone-software/why-did-you-render": "^8.0.3", "devcert": "^1.2.2", "eslint": "^9.14.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-hooks-addons": "^0.4.1", "eslint-plugin-react-hooks-extra": "^1.16.1", "prettier": "^3.3.3", "stylelint": "^16.23.1", "stylelint-config-standard": "^39.0.0", "typescript-eslint": "^8.14.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}