import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { DEFAULT_ORDER, DEFAULT_ORDER_BY } from "../../config/game.conf"
import { COMMUNITY_GAMES_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { GamesPage } from "../../pages/community/gamesPage/GamesPage"
import { filterGamesSearchSchema } from "../../schemas"
import { type GameStoreCollection } from "../../store/useGamesStore"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_GAMES_ROUTE } from "../paths"

import { communityRootRoute } from "./community.root.route"

const gamesSchema = z.object({})
export const gamesRouteSearchSchema = gamesSchema.extend(
  filterGamesSearchSchema.shape,
)

export const gamesRoute = createRoute({
  getParentRoute: () => communityRootRoute,
  path: PART_GAMES_ROUTE,
  validateSearch: (search) => gamesRouteSearchSchema.parse(search),
  staleTime: COMMUNITY_GAMES_STALE_TIME,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc, gameStore }, params: { communityId } }) => {
    try {
      const game = gameStore && gameStore.getGames(parseInt(communityId))

      if (game) {
        return
      }

      const gamesReturned = await Promise.all([
        trpc.community.games.list.query({
          communityId: parseInt(communityId),
        }),
      ])

      const newGame: GameStoreCollection = {
        ...gamesReturned[0],
        meta: {
          order: DEFAULT_ORDER,
          orderBy: DEFAULT_ORDER_BY,
          communityId: parseInt(communityId),
        },
      }

      gameStore?.setGames(newGame)
    } catch (error) {
      return handleLoaderErrors("Game list does not exist", error)
    }
  },
  component: GamesPage,
})
