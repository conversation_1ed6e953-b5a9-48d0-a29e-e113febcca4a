import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EditCommunityPage } from "../../pages/community/editCommunityPage/EditCommunityPage"
import { filterGamesSearchSchema } from "../../schemas"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_COMMUNITY_EDIT_ROUTE } from "../paths"

import { communityRootRoute } from "./community.root.route"

const tabsSchema = z.object({
  tab: z.string().optional().catch("main"),
})

export const communityEditRouteSearchSchema = tabsSchema.extend(
  filterGamesSearchSchema.shape,
)

export const communityEditRoute = createRoute({
  getParentRoute: () => communityRootRoute,
  path: PART_COMMUNITY_EDIT_ROUTE,
  validateSearch: (search) => communityEditRouteSearchSchema.parse(search),
  loader: async ({ context: { trpc }, params: { communityId } }) => {
    try {
      return await trpc.community.events.invites.query({
        communityId: parseInt(communityId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find community info", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EditCommunityPage,
})
