import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { COMMUNITY_GAMES_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { CommunityEventsPage } from "../../pages/community/communityEventsPage/CommunityEventsPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_COMMUNITY_EVENTS } from "../paths"

import { communityRootRoute } from "./community.root.route"

const communityEventsSchema = z.object({
  page: z.number().optional().catch(1),
  period: z.enum(["past", "future", "current", "active"]).optional(),
  owned: z.enum(["host", "my", "all"]).optional(), // "my" = means any event where user has any status
  search: z.string().optional(),
})

export const communityEventsRoute = createRoute({
  getParentRoute: () => communityRootRoute,
  path: PART_COMMUNITY_EVENTS,
  validateSearch: (search) => communityEventsSchema.parse(search),
  staleTime: COMMUNITY_GAMES_STALE_TIME,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc }, params: { communityId }, location }) => {
    try {
      const search = location.search as z.infer<typeof communityEventsSchema>
      return await trpc.protected.event.list.query({
        communityId: parseInt(communityId),
        period: search.period,
        owned: search.owned,
      })
    } catch (error) {
      return handleLoaderErrors(
        "You don't have access to community events",
        error,
      )
    }
  },
  component: CommunityEventsPage,
})
