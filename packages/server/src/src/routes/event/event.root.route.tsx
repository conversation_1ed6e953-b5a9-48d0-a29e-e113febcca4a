import { Outlet, createRoute, redirect } from "@tanstack/react-router"
import { TanStackRouterDevtools } from "@tanstack/router-devtools"

import { EVENT_STALE_TIME } from "../../config/routes"
import { LayoutEvent } from "../../layout/LayoutEvent/LayoutEvent"
import { Navigation } from "../../layout/Navigation/Navigation"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { GeneralErrorPage } from "../../pages/common/generalErrorPage/GeneralErrorPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { EVENT_ROOT_ROUTE, INDEX_ROUTE } from "../paths"
import { rootRoute } from "../root"

export const eventRootRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: EVENT_ROOT_ROUTE,
  staleTime: EVENT_STALE_TIME,
  beforeLoad: ({ context: { userData } }) => {
    if (!userData || !userData.isLoggedIn) {
      throw redirect({
        to: INDEX_ROUTE,
      })
    }
  },
  loader: async ({ context: { trpc }, params: { eventId } }) => {
    try {
      return await trpc.event.basic.query({
        eventId: parseInt(eventId),
      })
    } catch (error) {
      return handleLoaderErrors("Event not found!", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  notFoundComponent: (data) => (
    <>
      <Navigation pad type="event" />
      <LayoutEvent>
        <GeneralErrorPage data={data} />
      </LayoutEvent>
      {ENV_MODE === "dev" && <TanStackRouterDevtools />}
    </>
  ),
  component: () => {
    return (
      <>
        <Navigation pad type="event" />
        <LayoutEvent>
          <Outlet />
        </LayoutEvent>
        {ENV_MODE === "dev" && <TanStackRouterDevtools />}
      </>
    )
  },
})
