import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { EVENT_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EditEventPage } from "../../pages/event/editEventPage/EditEventPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_EVENT_EDIT_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

const eventInfoSchema = z.object({
  tab: z.string().optional(),
})

export const eventEditRoute = createRoute({
  validateSearch: (search) => eventInfoSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_EVENT_EDIT_ROUTE,
  staleTime: EVENT_STALE_TIME,
  loader: async ({ context: { trpc }, params: { eventId } }) => {
    try {
      return await trpc.event.extended.query({
        eventId: parseInt(eventId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find game", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EditEventPage,
})
