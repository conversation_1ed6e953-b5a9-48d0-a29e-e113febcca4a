import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { NO_CACHE_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { ParticipantPage } from "../../pages/event/participantPage/ParticipantPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_EVENT_PARTICIPANT_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

const eventInfoSchema = z.object({
  tab: z.string().optional(),
})

export const eventParticipantRoute = createRoute({
  validateSearch: (search) => eventInfoSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_EVENT_PARTICIPANT_ROUTE,
  staleTime: NO_CACHE_STALE_TIME,
  loader: async ({ context: { trpc }, params: { eventId, participantId } }) => {
    try {
      return await trpc.event.participant.info.query({
        eventId: parseInt(eventId),
        participantId: parseInt(participantId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find participant", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: ParticipantPage,
})
