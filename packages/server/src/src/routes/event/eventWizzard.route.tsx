import { createRoute } from "@tanstack/react-router"

import { NO_CACHE_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EventWizzardPage } from "../../pages/event/eventWizzardPage/EventWizzardPage"
import { filterGamesSearchSchema } from "../../schemas"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_EVENT_WIZZARD_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

export const eventWizzardRoute = createRoute({
  validateSearch: (search) => filterGamesSearchSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_EVENT_WIZZARD_ROUTE,
  staleTime: NO_CACHE_STALE_TIME,
  loader: async ({ context: { trpc }, params: { eventId } }) => {
    try {
      return await trpc.event.wizzard.settings.query({
        eventId: parseInt(eventId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find participant", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EventWizzardPage,
})
