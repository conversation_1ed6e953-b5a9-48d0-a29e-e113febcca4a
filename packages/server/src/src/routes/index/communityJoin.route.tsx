import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { JoinCommunityPage } from "../../pages/index/joinCommunityPage/JoinCommunityPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { COMMUNITY_JOIN_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

const communityJoinSchema = z.object({
  code: z.string(),
})

export const communityJoinRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  validateSearch: (search) => communityJoinSchema.parse(search),
  path: COMMUNITY_JOIN_ROUTE,
  loaderDeps: ({ search: { code } }) => ({ code }),
  loader: async ({ context: { trpc }, deps: { code } }) => {
    try {
      return await trpc.protected.community.do.inviteJoinPrejoin.query({
        code,
      })
    } catch (error) {
      return handleLoaderErrors("Can't join!", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: JoinCommunityPage,
})
