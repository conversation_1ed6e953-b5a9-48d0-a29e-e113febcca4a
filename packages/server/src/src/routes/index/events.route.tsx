import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { CHANGING_LISTS_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EventsPage } from "../../pages/index/eventsPage/EventsPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { EVENTS_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

const eventsSchema = z.object({
  page: z.number().optional().catch(1),
  period: z.enum(["past", "future", "current", "active"]).optional(),
  search: z.string().optional(),
  owned: z.enum(["host", "my", "all"]).optional(), // "my" = means any event where user has any status
})

export const eventsRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  staleTime: CHANGING_LISTS_STALE_TIME,
  path: EVENTS_ROUTE,
  validateSearch: (search) => eventsSchema.parse(search),
  loader: async ({ context: { trpc }, location }) => {
    try {
      const search = location.search as z.infer<typeof eventsSchema>
      return await trpc.protected.event.list.query({
        period: search.period,
        owned: search.owned,
      })
    } catch (error) {
      return handleLoaderErrors("Can't find events", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EventsPage,
})
