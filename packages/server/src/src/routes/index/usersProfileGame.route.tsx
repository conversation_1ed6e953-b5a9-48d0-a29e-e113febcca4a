import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { USER_PROFILE_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { UsersGamePage } from "../../pages/index/usersGamePage/UsersGamePage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PROFILE_GAME_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

const usersProfileGameSchema = z.object({
  search: z.string().optional(),
  sourcePage: z.string().optional(),
  sourceProps: z.string().optional(),
  sourceParams: z.string().optional(),
})

export const usersProfileGameRoute = createRoute({
  getParentRoute: () => indexRootRoute,
  validateSearch: (search) => usersProfileGameSchema.parse(search),
  path: PROFILE_GAME_ROUTE,
  staleTime: USER_PROFILE_STALE_TIME,
  pendingComponent: LoadingPage,
  pendingMs: 300,
  loader: async ({ context: { trpc }, params: { gameId } }) => {
    try {
      return await trpc.protected.user.games.query({
        gameId: parseInt(gameId),
      })
    } catch (error) {
      return handleLoaderErrors("No game information available", error)
    }
  },
  component: UsersGamePage,
})
