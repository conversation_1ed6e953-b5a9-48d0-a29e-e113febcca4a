import { type User } from "firebase/auth"
import { useCallback, useEffect, useState } from "react"

import { useUserStore } from "../store/useUserStore"
import { type RouterOutput, trpc } from "../trpc/trpc"
import { auth as authStorage } from "../utils/access"

import { useFirebase } from "./useFirebase"

type UserDataLoaderOutput = RouterOutput["loggedInUser"]

const REFRESH_TOKEN_INTERVAL = 30 * 60 * 1000

export const useUserDataLoader = () => {
  const { setUserData, setUserLoaded, clearUserData } = useUserStore()
  const [user, setUser] = useState<User | null>(null)
  const [fetchCount, setFetchCount] = useState(0)
  const { user: firebaseUser, refreshToken, userExists } = useFirebase()

  useEffect(() => {
    setUser(firebaseUser)
  }, [firebaseUser])

  useEffect(() => {
    try {
      const interval = setInterval(refreshToken, REFRESH_TOKEN_INTERVAL)
      return () => clearInterval(interval)
    } catch (error) {
      console.error(error)
      return () => {}
    }
  }, [refreshToken])

  useEffect(() => {
    const loadUserData = async () => {
      if (user) {
        // User is authenticated, get Firebase ID token and load user data
        try {
          authStorage.authToken = await user.getIdToken(true)
          const res: UserDataLoaderOutput = await trpc.login.do.login.query()
          if (res) {
            setUserData(res)
          }
        } catch (error) {
          console.error(error)
          setUserLoaded()
        }
      } else if (userExists === false) {
        // User is not authenticated and loading is complete
        setUserLoaded()
        clearUserData()
      }
    }

    if (userExists !== null) {
      loadUserData()
    }
    // eslint-disable-next-line react-hooks-addons/no-unused-deps
  }, [user, userExists, setUserData, setUserLoaded, fetchCount])

  const reFetch = useCallback(async () => {
    setFetchCount((prev: number) => prev + 1)
  }, [setFetchCount])

  return { user, reFetch }
}
