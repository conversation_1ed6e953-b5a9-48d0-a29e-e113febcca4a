export { type AppRouter } from "../../../../api/src/root/router"

import { type ArrayElement, type RouterOutput } from "../trpc/trpc"

// CommunityExpanded

export type IGamesAll = RouterOutput["community"]["games"]["list"]

export type ICommunityGame = ArrayElement<IGamesAll["games"]>

export type ITags = IGamesAll["tags"]

export type ITag = ArrayElement<ITags>

export type ITagCategories = IGamesAll["tagCategories"]
export type ITagCategory = ArrayElement<ITagCategories>

export type IGameUsers = IGamesAll["users"]
export type IGameUser = ArrayElement<IGameUsers>

export type IGameGameExtended2 = RouterOutput["community"]["games"]["game"]
export type IGameGameUsers = IGameGameExtended2["game"]["users"]
export type IGameGameUser = ArrayElement<IGameGameUsers>

export type IGameGame = IGameGameExtended2["game"]

export type IGameViewTags = IGameGameExtended2["tags"]
export type IGameViewTag = ArrayElement<IGameViewTags>

export type IGameViewExpansions = IGameGameExtended2["expansions"]

export type IGameViewExpansion = ArrayElement<IGameViewExpansions>

export type IUserProfileGames =
  RouterOutput["protected"]["user"]["info"]["games"]

export type IUserProfileGame = ArrayElement<IUserProfileGames>

export type IBasicCommunity = RouterOutput["community"]["basic"]

export type IUserGames = RouterOutput["protected"]["user"]["games"]

export type IUserProfileGameDetails = IUserGames["game"]

export type IUserProfileGameExpansions = IUserGames["expansions"]

export type IUserProfileGameData = IUserGames["myData"]

export type IUserProfileGameExpansion = ArrayElement<IUserProfileGameExpansions>

export type IItemInfo = RouterOutput["protected"]["game"]["itemInfo"]

export type IEventBaseData = RouterOutput["event"]["basic"]

export type IEventParticipant = ArrayElement<
  RouterOutput["event"]["participant"]["list"]
>

export type IEventUserSettingsWizzardState =
  RouterOutput["event"]["wizzard"]["settings"]["wizzardState"]

export interface ICEvent {
  id: number
  title: string
  starts: string
  ends?: string | null
  location?: string | null
  maxCapacity?: number | null
  state: string
  role?: string | null
  openness: string
  communities: ICEventHost[]
  reserveCapacity?: number | null
  going?: number | null
  reserve?: number | null
  smallDescription?: string | null
  hasAgeLimit?: boolean | null
  minCapacity?: number | null
  hostId?: number | null
  hostName?: string | null
  hostAvatar?: string | null
  hostColor?: string | null
  image?: string | null
  memberApproval?: boolean | null
  lat?: number | null
  lng?: number | null
}

export interface ICEventHost {
  id: number
  name: string
  image: string | null
  owner: boolean | null
}
