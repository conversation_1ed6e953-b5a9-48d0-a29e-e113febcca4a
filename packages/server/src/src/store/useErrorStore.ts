import { v4 as uuidv4 } from "uuid"
import { create } from "zustand"

import { isTRPCClientError } from "../trpc/trpc"

type ErrorMessage = {
  message: string
  code: string
  id: string
}

type UseErrorStoreProps = {
  pastErrors: ErrorMessage[]
  errors: ErrorMessage[]
  currentError: ErrorMessage | null
  isDisplaying: boolean
  setError: (error: Omit<ErrorMessage, "id">) => void
  markAsDisplayed: (error: ErrorMessage) => void
  showNextError: () => void
  setTrpcError: (error: unknown) => void
}

export const useErrorStore = create<UseErrorStoreProps>((set) => ({
  errors: [],
  pastErrors: [],
  currentError: null,
  isDisplaying: false,
  setTrpcError: (error: unknown) =>
    set((state) => {
      if (isTRPCClientError(error)) {
        const newError = {
          message: error.message,
          code: error.data?.code ?? "UNKNOWN",
          id: uuidv4(),
        }
        const newErrors = [...state.errors, newError]

        // If no error is currently being displayed, show this one immediately
        if (!state.isDisplaying && !state.currentError) {
          return {
            errors: newErrors.slice(1), // Remove the first error from queue
            currentError: newError,
            isDisplaying: true,
          }
        }

        return { errors: newErrors }
      }
      return state
    }),
  setError: (error: Omit<ErrorMessage, "id">) =>
    set((state) => {
      const newError = { ...error, id: uuidv4() }
      const newErrors = [...state.errors, newError]

      // If no error is currently being displayed, show this one immediately
      if (!state.isDisplaying && !state.currentError) {
        return {
          errors: newErrors.slice(1), // Remove the first error from queue
          currentError: newError,
          isDisplaying: true,
        }
      }

      return { errors: newErrors }
    }),
  markAsDisplayed: (error: ErrorMessage) =>
    set((state) => ({
      pastErrors: [...state.pastErrors, error],
      currentError: null,
      isDisplaying: false,
    })),
  showNextError: () =>
    set((state) => {
      if (state.errors.length > 0) {
        const nextError = state.errors[0]
        return {
          errors: state.errors.slice(1),
          currentError: nextError,
          isDisplaying: true,
        }
      }
      return {
        currentError: null,
        isDisplaying: false,
      }
    }),
}))
