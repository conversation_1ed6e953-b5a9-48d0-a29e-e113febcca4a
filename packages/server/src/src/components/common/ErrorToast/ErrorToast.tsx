import { Alert, Snackbar } from "@mui/material"
import { useEffect } from "react"

import { useErrorStore } from "../../../store/useErrorStore"

export const ErrorToast = () => {
  const currentError = useErrorStore((state) => state.currentError)
  const isDisplaying = useErrorStore((state) => state.isDisplaying)
  const { markAsDisplayed, showNextError } = useErrorStore()

  const handleClose = (reason?: string) => {
    if (reason === "clickaway") {
      return
    }
    if (currentError) {
      markAsDisplayed(currentError)
    }
  }

  // Show next error when current error is closed
  useEffect(() => {
    if (!isDisplaying && !currentError) {
      showNextError()
    }
  }, [isDisplaying, currentError, showNextError])

  useEffect(() => {
    if (!currentError) {
      return
    }
    console.info("Current error:", currentError)
  }, [currentError])

  if (!currentError || !isDisplaying) {
    return null
  }

  return (
    <Snackbar
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      open={isDisplaying}
      autoHideDuration={1200}
      onClose={(_, reason) => handleClose(reason)}
    >
      <Alert
        onClose={() => handleClose("close")}
        severity="error"
        variant="filled"
        sx={{ width: "100%" }}
      >
        {currentError.message}
      </Alert>
    </Snackbar>
  )
}
