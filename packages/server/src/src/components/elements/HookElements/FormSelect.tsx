import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material"
import { type FC } from "react"
import { Controller, useFormContext } from "react-hook-form"

interface FormInputProps {
  label: string
  name: string
  placeholder?: string
  helper?: string | React.ReactNode
  required?: boolean
  multiline?: boolean
  items: {
    title: string
    value: string
  }[]
}

export const FormSelect: FC<FormInputProps> = ({
  label,
  name,
  helper = "",
  items,
}) => {
  const { control } = useFormContext()
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <>
          <FormControl error={!!error} fullWidth>
            <InputLabel id={`${name}-label-id`}>{label}</InputLabel>
            <Select
              labelId={`${name}-label-id`}
              value={value}
              label={label}
              onChange={onChange}
            >
              {items.map((item) => (
                <MenuItem key={item.value} value={item.value}>
                  {item.title}
                </MenuItem>
              ))}
            </Select>
            {helper && (
              <Box>
                {typeof helper === "string" ? (
                  <Typography
                    variant="caption"
                    color="textSecondary"
                    dangerouslySetInnerHTML={{ __html: helper }}
                  />
                ) : (
                  helper
                )}
              </Box>
            )}
          </FormControl>
        </>
      )}
    />
  )
}
