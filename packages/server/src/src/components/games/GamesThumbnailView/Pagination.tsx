import { Box, Pagination as MuiPagination } from "@mui/material"

interface PaginationProps {
  page?: number
  pages: number
  onChange: (_: React.ChangeEvent<unknown>, page: number) => void
}

export const Pagination = ({ page = 1, pages, onChange }: PaginationProps) =>
  pages > 1 ? (
    <Box
      display="flex"
      justifyContent="center"
      flexDirection="row"
      alignItems="center"
    >
      <MuiPagination
        shape="rounded"
        count={pages}
        page={page}
        variant="outlined"
        onChange={onChange}
        sx={{
          "& .MuiPagination-ul": {
            flexWrap: "nowrap",
          },
        }}
      />
    </Box>
  ) : null
