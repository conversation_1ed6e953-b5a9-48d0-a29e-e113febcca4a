.card {
  width: 250px;
  cursor: pointer;
  position: relative;
}

.location {
  position: absolute;
  height: 22px;
  background-color: white;
  padding: 2px 2px 2px 4px;
  border-radius: 10px;
}

.text {
  position: relative;
}

.online {
  composes: location;
  top: 4px;
  right: 4px;
}

.map {
  composes: location;
  top: 34px;
  right: 4px;
}

.openness {
  composes: location;
  left: 4px;
  bottom: Calc(100% + 4px);
  padding: 2px 8px;
}

.member {
  composes: location;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  padding: 2px;
  top: 4px;
  left: 4px;
}

.image {
  width: 250px;
  height: 250px;
  object-fit: cover;
}

.invite {
    right: var(--spacing-h);
    bottom: 60px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    align-items: center;
    justify-content: center;
    position: absolute;
    background-color: white;
    padding: var(--spacing-1);
    border-radius: 10px;
}