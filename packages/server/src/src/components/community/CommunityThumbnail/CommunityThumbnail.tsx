import CardMembershipIcon from "@mui/icons-material/CardMembership"
import LanguageIcon from "@mui/icons-material/Language"
import MapIcon from "@mui/icons-material/Map"
import {
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Link,
  Typography,
} from "@mui/material"

import { COMMUNITY_IMAGES } from "../../../config/images"

import * as styles from "./communityThumbnail.module.css"

export interface ICCommunity {
  name: string
  id: number
  image: string
  location?: string | null
  online?: string | null
  openness: string
  approval?: boolean
  share?: boolean
  member?: boolean
  invite?: boolean
}
interface CommunityThumbnailProps {
  community: ICCommunity
  onClick: (id: number) => void
  onCancelInvite?: (id: number) => void
}
export const CommunityThumbnail = ({
  community,
  onClick,
  onCancelInvite,
}: CommunityThumbnailProps) => {
  const preventWeirdLink = (e: React.MouseEvent) => {
    e.stopPropagation()
  }
  return (
    <Card
      onClick={() => onClick(community.id)}
      className={styles.card}
      tabIndex={0}
      aria-label={`Open ${community.name}`}
    >
      <CardMedia
        component="img"
        className={styles.image}
        src={
          community.image
            ? `${ENV_IMAGE_CDN}${COMMUNITY_IMAGES}/large_${community.image}`
            : `${ENV_IMAGE_CDN}/${ENV_COMMUNITY_DEFAULT_LOGO}`
        }
      />
      <CardContent className={styles.text}>
        <Typography>{community.name}</Typography>
        {community.openness && (
          <Box
            className={styles.openness}
            display="flex"
            justifyContent="center"
            alignItems="center"
            title={community.openness.toLocaleUpperCase()}
          >
            <Typography variant="caption">
              {community.openness.toLocaleUpperCase()}
            </Typography>
          </Box>
        )}
      </CardContent>

      {community.member && (
        <Box
          className={styles.member}
          display="flex"
          justifyContent="center"
          alignItems="center"
          title="You are a member"
        >
          <CardMembershipIcon
            color="info"
            fontSize="large"
            aria-label="You are a member"
          />
        </Box>
      )}
      {community.invite && (
        <Box className={styles.invite} title="Invited community">
          <Typography variant="caption">Invited</Typography>
          <Button
            onClick={(e) => {
              e.stopPropagation()
              e.preventDefault()
              onCancelInvite?.(community.id)
            }}
            variant="contained"
            size="small"
            color="error"
          >
            Cancel
          </Button>
        </Box>
      )}
      {community.location && (
        <Box
          className={styles.map}
          display="flex"
          justifyContent="center"
          alignItems="center"
          title={community.location}
        >
          <Typography variant="caption">{community.location}</Typography>{" "}
          <MapIcon />
        </Box>
      )}
      {community.online && (
        <Box
          className={styles.online}
          display="flex"
          justifyContent="center"
          alignItems="center"
          title={community.online}
        >
          <Link
            href={community.online}
            underline="none"
            variant="caption"
            target="_blank"
            aria-label={`Open online link for ${community.name}`}
            onClick={preventWeirdLink}
          >
            Open link
          </Link>
          <LanguageIcon />
        </Box>
      )}
    </Card>
  )
}
