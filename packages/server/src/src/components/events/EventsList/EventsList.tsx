import { Box, Pagination } from "@mui/material"
import dayjs from "dayjs"
import Fuse from "fuse.js"
import { useCallback, useMemo } from "react"

import { EVENTS_PER_PAGE, SEARCH_STR_LENGTH } from "../../../config/game.conf"
import { useMapLocationStore } from "../../../store/useMapLocationStore"
import { ICEvent } from "../../../types/tRPC.types"
import { MapModal } from "../../modals/MapModal/MapModal"
import { EventThumbnail } from "../EventThumbnail/EventThumbnail"

interface EventsListProps {
  events: ICEvent[]
  onPageChange: (page: number) => void
  page?: number
  search?: string
}
export const EventsList = ({
  page,
  events,
  onPageChange,
  search,
}: EventsListProps) => {
  const location = useMapLocationStore((state) => state.location)
  const onChange = useCallback(
    (_: React.ChangeEvent<unknown>, page: number) => {
      onPageChange(page)
    },
    [onPageChange],
  )

  const filteredEvents = useMemo(() => {
    let eventsResult = events

    if (!search || search.length < SEARCH_STR_LENGTH) {
      return eventsResult
    }

    const filterOptionKeys = []

    filterOptionKeys.push({
      name: "title",
      getFn: (event: Pick<ICEvent, "title">) => {
        return event.title
      },
      weight: 0.7,
    })

    filterOptionKeys.push({
      name: "starts",
      getFn: (event: Pick<ICEvent, "starts">) => {
        return dayjs(event.starts).local().format("LLLL")
      },
      weight: 0.3,
    })

    filterOptionKeys.push({
      name: "ends",
      getFn: (event: Pick<ICEvent, "ends">) => {
        return event.ends ?? ""
      },
      weight: 0.3,
    })

    filterOptionKeys.push({
      name: "description",
      getFn: (event: Pick<ICEvent, "smallDescription">) => {
        return event.smallDescription ?? ""
      },
      weight: 0.5,
    })

    filterOptionKeys.push({
      name: "location",
      getFn: (event: Pick<ICEvent, "location">) => {
        return event.location ?? ""
      },
      weight: 0.5,
    })

    filterOptionKeys.push({
      name: "hostCommunity",
      weight: 0.5,
      getFn: (event: Pick<ICEvent, "communities">) =>
        event.communities !== undefined
          ? event.communities.map((host) => host.name ?? "")
          : "",
    })

    filterOptionKeys.push({
      name: "host",
      weight: 0.5,
      getFn: (event: Pick<ICEvent, "hostName">) => event.hostName ?? "",
    })

    const options = {
      minMatchCharLength: SEARCH_STR_LENGTH,
      threshold: 0.3,
      findAllMatches: true,
      includeScore: true,
      useExtendedSearch: true,
      keys: filterOptionKeys,
    }

    const fuseSearch = new Fuse(events, options)

    const searchProps = search
      .split(" ")
      .filter((s) => s.length >= SEARCH_STR_LENGTH)
      .join(" | ")

    const result = fuseSearch.search(searchProps)

    eventsResult = result.map((result) => {
      return result.item
    })

    return eventsResult
  }, [events, search])

  const pages = Math.ceil(filteredEvents.length / EVENTS_PER_PAGE)
  const startItem = EVENTS_PER_PAGE * ((page ?? 1) - 1)
  const endItem = startItem + EVENTS_PER_PAGE

  return (
    <Box
      display="flex"
      justifyContent="center"
      flexDirection="column"
      gap={4}
      pt={2}
      pb={8}
    >
      {pages > 1 && (
        <Box
          display="flex"
          justifyContent="center"
          flexDirection="row"
          alignItems="center"
        >
          <Pagination
            shape="rounded"
            count={pages}
            page={page}
            variant="outlined"
            onChange={onChange}
          />
        </Box>
      )}
      <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
        {filteredEvents.slice(startItem, endItem).map((event) => (
          <EventThumbnail key={event.id} event={event} />
        ))}
      </Box>
      {pages > 1 && (
        <Box
          display="flex"
          justifyContent="center"
          flexDirection="row"
          alignItems="center"
          pb={4}
        >
          <Pagination
            shape="rounded"
            count={pages}
            page={page}
            variant="outlined"
            onChange={onChange}
          />
        </Box>
      )}
      <MapModal event={location ?? undefined} />
    </Box>
  )
}
