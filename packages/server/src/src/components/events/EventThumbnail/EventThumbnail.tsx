import AccessTimeIcon from "@mui/icons-material/AccessTime"
import {
  <PERSON>ert,
  Box,
  Button,
  ButtonGroup,
  Card,
  CardActions,
  CardContent,
  CardMedia,
  Typography,
} from "@mui/material"

import { createImageLink } from "../../../config/images"
import { ICEvent } from "../../../types/tRPC.types"
import { localToLocalWithDay } from "../../../utils/transformTime"
import { EventStatus } from "../EventStatus/EventStatus"

import { EventButtons } from "./components/EventButtons"
import { HostInfo } from "./components/HostInfo"
import { MapInfo } from "./components/MapInfo"
import { StateLine } from "./components/StateLine"
import * as styles from "./eventThumbnail.module.css"

interface EventThumbnailProps {
  event: ICEvent
  invite?: boolean
  onAcceptInvite?: (id: number) => void
  onDeclineInvite?: (id: number) => void
}
export const EventThumbnail = ({
  event,
  invite = false,
  onAcceptInvite,
  onDeclineInvite,
}: EventThumbnailProps) => {
  return (
    <Card className={styles.card}>
      <CardMedia
        component="img"
        className={styles.image}
        src={createImageLink("event", "large", event.id, event.image)}
      />
      <CardContent>
        <Box display="flex" flexDirection="column" gap={2}>
          <Box display="flex" gap={1} alignItems="center" flexWrap="nowrap">
            <AccessTimeIcon fontSize="medium" />
            <Typography variant="h6">
              {localToLocalWithDay(event.starts, true)}
            </Typography>
          </Box>
          {event.ends && (
            <Box display="flex" gap={1} alignItems="center" flexWrap="nowrap">
              Till <AccessTimeIcon fontSize="small" />
              <Typography variant="subtitle1">
                {localToLocalWithDay(event.ends, true)}
              </Typography>
            </Box>
          )}
          <MapInfo event={event} />
          <Box>
            <Typography variant="h2" fontSize="1.2rem">
              {event.title}
            </Typography>
          </Box>
          {event.smallDescription && (
            <Box>
              <Typography variant="body1">{event.smallDescription}</Typography>
            </Box>
          )}
          <HostInfo event={event} />
        </Box>
      </CardContent>
      <CardActions>
        <Box display="flex" flexDirection="column" gap={1} width="100%">
          <StateLine event={event} />
          {invite && (
            <Box
              className={styles.invite}
              display="flex"
              flexDirection="column"
              gap={2}
            >
              <Alert severity="error">
                Your community has been invited to join this event. This means
                that all of Your members will be able to join this event and
                community will be visible to all event participants as one of
                the organizers.
              </Alert>
              <ButtonGroup>
                <Button
                  onClick={() => onAcceptInvite?.(event.id)}
                  variant="contained"
                >
                  Accept
                </Button>
                <Button
                  onClick={() => onDeclineInvite?.(event.id)}
                  variant="contained"
                  color="error"
                >
                  Decline
                </Button>
              </ButtonGroup>
            </Box>
          )}
          {!invite && (
            <Box className={styles.bottom}>
              <EventButtons event={event} source="card" />
              <EventStatus event={event} />
            </Box>
          )}
        </Box>
      </CardActions>
    </Card>
  )
}
