import { Box, Divider, List } from "@mui/material"

import { LoginButton } from "../../../components/common/LoginButton/LoginButton"
import { hasPermission } from "../../../permissions"
import { eventRootRoute } from "../../../routes/event/event.root.route"
import {
  EVENT_EDIT_ROUTE,
  EVENT_GAMES_ROUTE,
  EVENT_MANAGE_ROUTE,
  EVENT_ORGANIZERS_ROUTE,
  EVENT_PARTICIPANTS_ROUTE,
  EVENT_ROUTE_INFO,
  EVENT_WIZZARD_ROUTE,
  INDEX_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { NavigationItem } from "../components/NavigationItem"
import * as styles from "../navigation.module.css"

interface DrawerButtonsProps {
  toggleDrawer: () => void
}
export const DrawerButtons = ({ toggleDrawer }: DrawerButtonsProps) => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)

  const eventData = eventRootRoute.useLoaderData()
  const userInfo = useUserStore((state) => state.userData)

  const id = String(eventData?.id ?? 0)

  const onClose = () => {
    toggleDrawer()
  }

  return (
    <Box
      onClick={onClose}
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      alignItems="center"
      gap={2}
      height="100%"
      pb={8}
    >
      {isLoggedIn && (
        <>
          <List className={styles.list}>
            <NavigationItem
              text={eventData?.title ?? ""}
              route={EVENT_ROUTE_INFO}
              params={{ eventId: id }}
              isMain
            />
            {hasPermission(userInfo, "event", "shareUserGames", eventData) && (
              <NavigationItem
                text="Wizzard!"
                route={EVENT_WIZZARD_ROUTE}
                params={{ eventId: id }}
              />
            )}
            <NavigationItem
              text="Games"
              route={EVENT_GAMES_ROUTE}
              params={{ eventId: id }}
            />
            <NavigationItem
              text="Participants"
              route={EVENT_PARTICIPANTS_ROUTE}
              params={{ eventId: id }}
            />
            <NavigationItem
              text="Organizers"
              route={EVENT_ORGANIZERS_ROUTE}
              params={{ eventId: id }}
            />
            {hasPermission(userInfo, "event", "update", {
              id: Number(id),
            }) && (
              <NavigationItem
                text="Administrate"
                route={EVENT_EDIT_ROUTE}
                params={{ eventId: id }}
              />
            )}
            <NavigationItem
              text="Profile"
              route={EVENT_MANAGE_ROUTE}
              params={{ eventId: id }}
            />
          </List>
          <Divider sx={{ width: "100%" }} />
        </>
      )}
      <Box>
        <List>
          <NavigationItem text="Home" route={INDEX_ROUTE} />
        </List>
        <LoginButton />
      </Box>
    </Box>
  )
}
