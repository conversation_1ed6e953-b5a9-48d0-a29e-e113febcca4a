import { Box, Button, Typography } from "@mui/material"
import { use<PERSON><PERSON><PERSON>, useRouter } from "@tanstack/react-router"
import { useCallback } from "react"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import {
  CommunityThumbnail,
  ICCommunity,
} from "../../../components/community/CommunityThumbnail/CommunityThumbnail"
import {
  INVITE_COMMUNITY_MODAL_NAME,
  InviteCommunityModal,
} from "../../../components/modals/InviteCommunityModal/InviteCommunityModal"
import { UserList } from "../../../components/user/UserList/UserList"
import { eventOrganizersRoute } from "../../../routes/event/eventOrganizers.route"
import { COMMUNITY_ROUTE, EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { useErrorStore } from "../../../store/useErrorStore"
import { useModalStore } from "../../../store/useModalStore"
import { trpc } from "../../../trpc/trpc"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

export const EventOrganizersPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const { openModal } = useModalStore()
  const { setTrpcError } = useErrorStore()
  const router = useRouter()
  const data = eventOrganizersRoute.useLoaderData()

  const navigate = useNavigate()

  const onCommunityClick = useCallback(
    (id: number) => {
      navigate({ to: COMMUNITY_ROUTE, params: { communityId: id.toString() } })
    },
    [navigate],
  )

  const onCancelInvite = useCallback(
    async (id: number) => {
      if (!event || !isEvent(event) || !event.id) {
        return
      }
      try {
        await trpc.event.community.do.cancelInvite.mutate({
          eventId: event.id,
          communityId: id,
        })
        router.invalidate()
      } catch (error: unknown) {
        setTrpcError(error)
      }
    },
    [event],
  )

  if (!data || !event || !isEvent(event)) {
    return null
  }

  const { communities, hosts } = data

  return (
    <>
      <TitleRow title="Organizers">
        <Button
          variant="outlined"
          onClick={() => openModal(INVITE_COMMUNITY_MODAL_NAME)}
        >
          Invite Community
        </Button>
      </TitleRow>
      <Box>
        <Typography variant="h6">Communities</Typography>
        <Box
          alignContent="center"
          justifyContent="center"
          display="flex"
          flexDirection="row"
          gap={4}
          flexWrap="wrap"
          padding={5}
        >
          {communities.map((community) => (
            <CommunityThumbnail
              onCancelInvite={onCancelInvite}
              key={community.id}
              community={community as ICCommunity}
              onClick={() => onCommunityClick(community.id)}
            />
          ))}
        </Box>
      </Box>
      {hosts && (
        <Box>
          <Typography variant="h6">Hosts</Typography>
          <Box>
            <UserList users={hosts} eventId={event.id} labelInfo="Host: " />
          </Box>
        </Box>
      )}
      <InviteCommunityModal eventId={event.id} />
    </>
  )
}
