import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Stepper } from "@mui/material"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { eventWizzardRoute } from "../../../routes/event/eventWizzard.route"
import { EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

import { Games } from "./steps/Games"
import { Review } from "./steps/Review"
import { Settings } from "./steps/Settings"

const stepMap: Record<
  "waiting" | "pick-games" | "review-games" | "settings",
  number
> = {
  waiting: 0,
  settings: 0,
  "pick-games": 1,
  "review-games": 2,
}
export const EventWizzardPage = () => {
  const settings = eventWizzardRoute.useLoaderData()
  const base = useParentRouteData(EVENT_ROOT_ROUTE)
  const user = useUserStore((state) => state.userData)

  if (!isEvent(base) || !user) {
    return null
  }

  const canShare = hasPermission(user, "event", "shareUserGames", base)
  if (!canShare) return null

  const state:
    | "settings"
    | "waiting"
    | "pick-games"
    | "review-games"
    | "done"
    | "skip" = settings?.wizzardState ?? "waiting"

  if (state === "done" || state === "skip") {
    return null
  }

  return (
    <>
      <TitleRow title="Pick games to bring" />
      <Box p={2} display="flex" flexDirection="column" gap={4}>
        <Stepper activeStep={stepMap[state]}>
          <Step key="waiting">
            <StepLabel>Settings</StepLabel>
          </Step>
          <Step key="pick-games">
            <StepLabel>Pick Games</StepLabel>
          </Step>
          <Step key="review-games">
            <StepLabel>Review Games</StepLabel>
          </Step>
        </Stepper>
        <Box>
          {(state === "waiting" || state === "settings") && <Settings />}
          {state === "pick-games" && <Games />}
          {state === "review-games" && <Review />}
        </Box>
      </Box>
    </>
  )
}
