import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback, useMemo } from "react"

import {
  GameSearch,
  type SearchParams,
} from "../../../../components/games/GameSearch/GameSearch"
import {
  type ICTag,
  type ICTagCat,
} from "../../../../components/games/GameSearch/components/ChipList"
import { type ICSearchBoxTag } from "../../../../components/games/GameSearch/components/SearchBox"
import { GamesThumbnailView } from "../../../../components/games/GamesThumbnailView/GamesThumbnailView"
import { SearchModal } from "../../../../components/modals/SearchModal/SearchModal"
import { usersProfileRoute } from "../../../../routes/index/usersProfile.route"
import { PROFILE_GAME_ROUTE, PROFILE_ROUTE } from "../../../../routes/paths"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"
import { applyFilters } from "../../../../utils/filter"

import type { IUserProfileGame } from "../../../../types/tRPC.types"

interface UserExpansionsProps {
  games: IUserProfileGame[]
  tags?: ICSearchBoxTag[]
  tagCategories?: ICTagCat[]
}

export const UserGames = ({
  games,
  tags,
  tagCategories,
}: UserExpansionsProps) => {
  const search = usersProfileRoute.useSearch()
  const params = usersProfileRoute.useParams()
  const navigate = useNavigate()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const isLargeTablet = sizeThresholdList.largeTablet

  const onChange = useCallback(
    (page: number) => {
      navigate({
        to: PROFILE_ROUTE,
        params,
        search: {
          ...search,
          page,
          tab: "games",
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate, params, search],
  )

  const onNavigate = useCallback(
    (search: SearchParams) =>
      navigate({
        to: PROFILE_ROUTE,
        params,
        search: {
          ...search,
          tab: "games",
        },
      }),
    [navigate, params],
  )

  const optimizeTags = useMemo(() => {
    const populatedTags: ICTag[][] = []

    games.forEach((currentGame) => {
      populatedTags[currentGame.id] = currentGame.tags!.map(
        (currentTag) => (tags ?? []).find((tag) => tag.id === currentTag)!,
      )
    })

    return populatedTags
  }, [tags])

  const useGameList = useMemo(() => {
    return applyFilters(games, search, optimizeTags)
  }, [games, search])

  return (
    <Box
      display="flex"
      gap={4}
      flexWrap="wrap"
      justifyContent="center"
      alignItems="center"
      flexDirection="column"
      width="100%"
      padding={2}
      boxSizing="border-box"
    >
      <GameSearch
        search={search}
        onNavigate={onNavigate}
        personalOrder
        tags={tags}
        tagCategories={tagCategories}
      />
      <SearchModal
        search={search}
        onNavigate={onNavigate}
        personalOrder
        tags={tags}
        tagCategories={tagCategories}
      />
      <GamesThumbnailView
        listMode={isLargeTablet}
        games={useGameList}
        navigation={{
          to: PROFILE_GAME_ROUTE,
          search: {
            sourcePage: "user",
            sourceProps: JSON.stringify(search),
          },
          params: {},
        }}
        onPageChange={onChange}
        page={search.page ?? 1}
      />
    </Box>
  )
}
