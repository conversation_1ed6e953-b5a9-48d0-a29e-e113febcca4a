import { Box, FormControlLabel, Radio, RadioGroup } from "@mui/material"

import { ICThumbnailGame } from "../../../../components/games/GameThumbnail/type"

import * as styles from "./extendedGameThumbnail.module.css"

type ExtendedGameThumbnailProps = {
  game: ICThumbnailGame
  canRequest: boolean
  onChange: (gameId: number, value: string) => void
}
export const ExtendedGameThumbnail = ({
  game,
  canRequest,
  onChange,
}: ExtendedGameThumbnailProps) => {
  return (
    <Box className={styles.container}>
      <RadioGroup
        defaultValue={canRequest ? "canask" : "willnotbring"}
        name="game-option"
        onChange={(event) => onChange(game.id, event.target.value)}
      >
        <FormControlLabel
          value="willbring"
          control={<Radio />}
          label="In the bag"
        />
        <FormControlLabel value="canask" control={<Radio />} label="Ask me" />
        <FormControlLabel
          value="willnotbring"
          control={<Radio />}
          label="Nope!"
        />
      </RadioGroup>
    </Box>
  )
}
