import {
  <PERSON>,
  Button,
  FormControlLabel,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
} from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { ChangeEvent, useCallback, useState } from "react"

import { CommunityThumbnail } from "../../../components/community/CommunityThumbnail/CommunityThumbnail"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { communityJoinRoute } from "../../../routes/index/communityJoin.route"
import { COMMUNITY_OPEN_ROUTE, COMMUNITY_ROUTE } from "../../../routes/paths"
import { useErrorStore } from "../../../store/useErrorStore"
import { trpc } from "../../../trpc/trpc"

import * as styles from "./joinCommunityPage.module.css"

export const JoinCommunityPage = () => {
  const { code } = communityJoinRoute.useSearch()
  const [share, setShare] = useState(true)
  const community = communityJoinRoute.useLoaderData()
  const { setTrpcError } = useErrorStore()
  const navigate = useNavigate()

  if (!community) {
    return null
  }

  const handleJoin = useCallback(async () => {
    try {
      const result = await trpc.protected.community.do.inviteJoin.mutate({
        code,
        share,
      })

      navigate({
        to: COMMUNITY_ROUTE,
        params: { communityId: String(result.communityId) },
      })
    } catch (error: unknown) {
      setTrpcError(error)
    }
  }, [code, share, navigate])

  return (
    <Box className={styles.container}>
      <Typography variant="h6">
        Do You really want to join this community?
      </Typography>
      <Box className={styles.community}>
        <CommunityThumbnail
          community={community?.community}
          onClick={() => {}}
        />
      </Box>
      <Box>
        {community?.community.share && (
          <FormControlLabel
            control={
              <Switch
                checked={share}
                onChange={(_: ChangeEvent<HTMLInputElement>, value) =>
                  setShare(value)
                }
              />
            }
            label="Share my collection"
          />
        )}
      </Box>
      <Box className={styles.buttons}>
        <Button variant="contained" onClick={handleJoin}>
          Yes, Join!
        </Button>
        <PartyLink
          variant="contained"
          color="inherit"
          to={COMMUNITY_OPEN_ROUTE}
        >
          No, go back!
        </PartyLink>
      </Box>
    </Box>
  )
}
