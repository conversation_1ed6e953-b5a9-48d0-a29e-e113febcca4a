import { Box, Button, Typography } from "@mui/material"
import { useNavigate, useRouter } from "@tanstack/react-router"
import { useCallback } from "react"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { BggLink } from "../../../components/games/BggLink/BggLink"
import { ChipsList } from "../../../components/games/ChipsList/ChipsList"
import { GameInfo } from "../../../components/games/GameInfo/GameInfo"
import { OwnerInfo } from "../../../components/user/OwnerInfo/OwnerInfo"
import { GAME_IMAGES } from "../../../config/images"
import { usersProfileGameRoute } from "../../../routes/index/usersProfileGame.route"
import {
  EVENT_WIZZARD_ROUTE,
  INDEX_ROUTE,
  PROFILE_ROUTE,
} from "../../../routes/paths"

import { ExpansionList } from "./components/ExpansionList"
import * as styles from "./usersGamePage.module.css"

const backLinks: Record<string, string> = {
  games: INDEX_ROUTE,
  user: PROFILE_ROUTE,
  eventWizzard: EVENT_WIZZARD_ROUTE,
}

export const UsersGamePage = () => {
  const game = usersProfileGameRoute.useLoaderData()
  const router = useRouter()
  const navigate = useNavigate()
  const search = usersProfileGameRoute.useSearch()

  const handleBack = useCallback(() => {
    navigate({
      to: !search.sourcePage ? "INDEX_ROUTE" : backLinks[search.sourcePage],
      search: search.sourceProps ? JSON.parse(search.sourceProps) : {},
      params: search.sourceParams ? JSON.parse(search.sourceParams) : {},
    })
  }, [router])

  if (!game) return null

  return (
    <Box pb={8}>
      <TitleRow title={game.game.title}>
        <Button variant="outlined" onClick={handleBack}>
          Back
        </Button>
      </TitleRow>
      <Box className={styles.firstRowWrapper}>
        <Box className={styles.gameInfoWrapper}>
          <Box display="flex" justifyContent="center">
            <Box className={styles.imageBox}>
              <img
                src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.game.id}.jpg`}
                alt={game.game.title}
              />
              <Box className={styles.link}>
                <BggLink bggId={game.game.bggId} />
                {game.game.average && (
                  <Box className={styles.bggRating}>
                    <Typography fontWeight={600}>
                      {Math.round(game.game.average * 10) / 10}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
          <GameInfo game={game.game} />
        </Box>
        <OwnerInfo user={game.myData} />
      </Box>
      <Box className={styles.borderBox} mt={2}>
        <ChipsList tagList={game.tags} />
      </Box>
      {game.expansions.length > 0 && (
        <Box pt={2} className={styles.borderBox}>
          <Box pt={2}>
            <ExpansionList expansions={game.expansions} />
          </Box>
        </Box>
      )}
    </Box>
  )
}
