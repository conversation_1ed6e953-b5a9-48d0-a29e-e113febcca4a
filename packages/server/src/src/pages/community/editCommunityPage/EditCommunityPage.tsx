import { Box } from "@mui/material"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { hasPermission } from "../../../permissions"
import { communityEditRoute } from "../../../routes/community/communityEdit.route"
import { COMMUNITIES_ROOT_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

import { EventInvites } from "./components/EventInvites"
import { MenuBar } from "./components/MenuBar"
import { Settings } from "./components/Settings"
import * as styles from "./editCommunityPage.module.css"

export const EditCommunityPage = () => {
  const search = communityEditRoute.useSearch()

  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)

  const user = useUserStore((state) => state.userData)

  if (!isCommunity(base)) {
    return null
  }

  if (!hasPermission(user, "community", "update", { id: base.id })) {
    return null
  }

  return (
    <Box className={styles.container}>
      <TitleRow title="Administrate" />
      <MenuBar communityId={base.id} />
      <Box py={2}>
        {(!search.tab || search.tab === "main") && <Settings />}
        {search.tab === "invites" && <EventInvites />}
      </Box>
    </Box>
  )
}
