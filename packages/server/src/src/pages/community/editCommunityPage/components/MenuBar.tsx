import EditCalendarIcon from "@mui/icons-material/EditCalendar"
import SettingsIcon from "@mui/icons-material/Settings"
import { Tab, Tabs } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { SyntheticEvent, useCallback } from "react"

import { communityEditRoute } from "../../../../routes/community/communityEdit.route"
import { COMMUNITY_EDIT_ROUTE } from "../../../../routes/paths"

interface MenuBarProps {
  communityId: number
}
export const MenuBar = ({ communityId }: MenuBarProps) => {
  const search = communityEditRoute.useSearch()
  const navigate = useNavigate()
  const handleChange = useCallback(
    (_: SyntheticEvent<Element, Event>, value: string) => {
      navigate({
        to: COMMUNITY_EDIT_ROUTE,
        params: {
          communityId: String(communityId),
        },
        search: {
          tab: value,
        },
      })
    },
    [navigate],
  )

  return (
    <Tabs value={search.tab ?? "main"} onChange={handleChange}>
      <Tab value="main" tabIndex={0} icon={<SettingsIcon />} label="Settings" />
      <Tab
        value="invites"
        tabIndex={0}
        icon={<EditCalendarIcon />}
        label="Event Invites"
      />
    </Tabs>
  )
}
