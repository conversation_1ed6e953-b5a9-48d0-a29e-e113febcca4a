import { useRouter } from "@tanstack/react-router"
import React, { useEffect } from "react"

import { CommunityConfiguration } from "../../../../components/community/CommunityConfiguration/CommunityConfiguration"
import { CommunityUploadImage } from "../../../../components/community/CommunityUploadImage/CommunityUploadImage"
import { COMMUNITIES_ROOT_ROUTE } from "../../../../routes/paths"
import {
  isCommunity,
  useParentRouteData,
} from "../../../../utils/pages.rootObject"

export const Settings = () => {
  const [savedImage, setSavedImage] = React.useState<number>(0)
  const [saved, setSaved] = React.useState(false)
  const router = useRouter()
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)

  useEffect(() => {
    if (savedImage > 0) {
      router.invalidate()
    }
  }, [savedImage, router])

  if (!isCommunity(base)) {
    return null
  }

  return (
    <>
      <CommunityUploadImage
        id={base.id}
        onSuccess={() => setSavedImage(savedImage + 1)}
        hasImage={base.image}
        notNew={true}
      />
      <CommunityConfiguration edit={base} onSuccess={() => setSaved(true)} />
    </>
  )
}
