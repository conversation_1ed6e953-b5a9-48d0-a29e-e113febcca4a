import { useMemo } from "react"

import { GameThumbnail } from "../../../../components/games/GameThumbnail/GameThumbnail"
import {
  type ICNavigationProps,
  type ICThumbnailGame,
} from "../../../../components/games/GameThumbnail/type"
import {
  type ICAvatarUser,
  type ICFAvatarOnClick,
} from "../../../../components/user/UserAvatar/UserAvatar"
import { isNewEntry } from "../../../../utils/transformTime"

export type ICCommunityThumbnailGame = ICThumbnailGame & {
  news: string | null
  users: [number, number][]
  average: number
}

interface GameThumbnailProps {
  game: ICCommunityThumbnailGame
  communityId: number
  onClick: ICFAvatarOnClick
  lookupUsers: ICAvatarUser[]
  navigation?: ICNavigationProps
}
export const GameThumbnailWrapper = ({
  game,
  lookupUsers,
  communityId,
  onClick,
  navigation,
}: GameThumbnailProps) => {
  const isNew = !isNewEntry(game.news)

  const userList = useMemo(() => {
    return game.users
      .map((user) => lookupUsers.find((look) => look.id == user[0]))
      .filter((user) => user !== undefined)
  }, [game.users, lookupUsers])

  return (
    <GameThumbnail
      navigation={navigation}
      game={game}
      communityId={communityId}
      onUser={onClick}
      userList={userList}
      isNew={isNew}
    />
  )
}
