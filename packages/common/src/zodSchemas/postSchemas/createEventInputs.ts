import { z } from "zod"

const eventInputs = {
  id: z.number().optional(),
  title: z.string().min(3),
  openness: z.enum([
    "public",
    "publicLimited",
    "community",
    "seniority",
    "private",
    "closed",
  ]),
  location: z.union([
    z.literal(""),
    z.string().min(3).max(256).optional(),
    z.null(),
  ]),
  approval: z.boolean(),
  description: z.union([
    z.literal(""),
    z.string().min(10).optional(),
    z.null(),
  ]),
  starts: z.string().datetime({ offset: true }),
  ends: z.string().datetime({ offset: true }).nullable().optional(),
  maxCapacity: z.number().nullable().optional(),
  reserveCapacity: z.number().nullable().optional(),
  state: z.enum(["open", "ongoing", "ended", "hidden", "cancelled"]),
  share: z.enum(["none", "all", "hosts", "host-managed", "single-game"]),
  hasAgeLimit: z.boolean(),
  minCapacity: z.number().nullable().optional(),
  smallDescription: z.union([
    z.literal(""),
    z.string().min(10).optional(),
    z.null(),
  ]),
  lat: z.number().nullable().optional(),
  lng: z.number().nullable().optional(),
}

export const createEventInputs = z.object({
  ...eventInputs,
  openness: z.enum([
    "public",
    "publicLimited",
    "community",
    "seniority",
    "private",
    "closed",
  ]),
})

export const createPrivateEventInputs = z.object({
  ...eventInputs,
  openness: z.enum([
    "public",
    "publicLimited",
    "community",
    "seniority",
    "private",
    "closed",
  ]),
})

export type CreateEventInputs = z.infer<typeof createEventInputs>
export type CreatePrivateEventInputs = z.infer<typeof createPrivateEventInputs>
