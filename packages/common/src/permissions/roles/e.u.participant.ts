import { RoleWithPermissionsAndProperties } from "../general"
import { check } from "./checks/check"

export const participant: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: false,
      isParticipant: true,
      view: true,
      viewPublic: true,
      shareUserGames: ({ user, data: event }) =>
        check(
          (event.share ?? false) === "all" && user.id === event.viewedUserId,
        ),
    },
  },
  properties: {
    subject: "event",
    level: "user",
  },
}
