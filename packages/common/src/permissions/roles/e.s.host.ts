import { RoleWithPermissionsAndProperties } from "../general"
import { eventJoin } from "./checks/event.join"
import { check } from "./checks/check"
import { hasPermission } from "../hasPermissions"

export const host: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: eventJoin,
      isHost: true,
      isSuper: true,
      promoteCohost: true,
      update: true,
      delete: true,
      approve: true,
      shareUserGames: ({ user, data: event }) => {
        const share = event.share ?? "none"

        return check(
          share === "all" ||
            share === "host-managed" ||
            (share === "hosts" &&
              hasPermission(user, "event", "isSuper", event)),
        )
      },
    },
  },
  properties: {
    subject: "event",
    level: "super",
  },
}
