import { RoleWithPermissionsAndProperties } from "../general"
import { check } from "./checks/check"
import { hasPermission } from "../hasPermissions"

export const cohost: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: false,
      update: true,
      approve: true,
      shareUserGames: ({ user, data: event }) => {
        const share = event.share ?? "none"

        return check(
          share === "all" ||
            share === "host-managed" ||
            (share === "hosts" &&
              hasPermission(user, "event", "isSuper", event)),
        )
      },
      isSuper: true,
      isCohost: true,
    },
  },
  properties: {
    subject: "event",
    level: "super",
  },
}
