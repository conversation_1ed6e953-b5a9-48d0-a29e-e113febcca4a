import { cancelInviteCommunity } from "./mutations/cancelInviteCommunity"
import { changeMyEventStatus } from "./mutations/changeMyEventStatus"
import { changeUserEventStatus } from "./mutations/changeUserEventStatus"
import { changeUserStatus } from "./mutations/changeUserStatus"
import { configureCommunity } from "./mutations/configureCommunity"
import { configureEvent } from "./mutations/configureEvent"
import { confirmCommunityEventInvite } from "./mutations/confirmCommunityEventInvite"
import { createCommunity } from "./mutations/createCommunity"
import { createEvent } from "./mutations/createEvent"
import { createInvite } from "./mutations/createInvite"
import { createPrivateEvent } from "./mutations/createPrivateEvent"
import { declineCommunityEventInvite } from "./mutations/declineCommunityEventInvite"
import { inviteCommunity } from "./mutations/inviteCommunity"
import { joinByInvite } from "./mutations/joinByInvite"
import { joinCommunity } from "./mutations/joinCommunity"
import { leaveCommunity } from "./mutations/leaveCommunity"
import { switchGameShare } from "./mutations/switchGameShare"
import { userProfileUpdate } from "./mutations/userProfileUpdate"
import { communityBasic } from "./queries/community/communityBasic"
import { communityExpanded2 } from "./queries/community/communityExpanded2"
import { communityGameExtended2 } from "./queries/community/communityGameExtended2"
import { communityGamesList2 } from "./queries/community/communityGamesList2"
import { communityInvites } from "./queries/community/communityInvites"
import { communityList } from "./queries/community/communityList"
import { communityUserInfo } from "./queries/community/communityUserInfo"
import { communityUserList } from "./queries/community/communityUserList"
import { joinByInvitePrejoin } from "./queries/community/joinByInvitePrejoin"
import { publicCommunityList } from "./queries/community/publicCommunityList"
import { eventBasic } from "./queries/event/eventBasic"
import { eventExtended } from "./queries/event/eventExtended"
import { eventGamesList } from "./queries/event/eventGamesList"
import { eventOrganizers } from "./queries/event/eventOrganizers"
import { eventParticipant } from "./queries/event/eventParticipant"
import { eventParticipantList } from "./queries/event/eventParticipantList"
import { eventsList } from "./queries/event/eventsList"
import { updateGameChangeStatus } from "./queries/event/games/do/updateGameChangeStatus"
import { eventWizzardCreatePreset } from "./queries/event/wizzard/do/eventWizzardCreatePreset"
import { eventWizzardUpdateSettings } from "./queries/event/wizzard/do/eventWizzardUpdateSettings"
import { eventWizzardUpdateSettingsExtended } from "./queries/event/wizzard/do/eventWizzardUpdateSettingsExtended"
import { eventWizzardSettings } from "./queries/event/wizzard/eventWizzardSettings"
import { getItemInfo } from "./queries/game/getItemInfo"
import { getMyGameInfo } from "./queries/game/getMyGameInfo"
import { protectedEventDeletePreset } from "./queries/protected/event/do/protectedEventDeletePreset"
import { searchCommunities } from "./queries/search/searchCommunities"
import { tagList } from "./queries/tagList"
import { getMyInfo } from "./queries/user/getMyInfo"
import { loggedInUser } from "./queries/user/loggedInUser"
import { logoutUser } from "./queries/user/logoutUser"
import { router } from "./trpc/trpc"

export const appRouter = router({
  login: {
    do: {
      login: loggedInUser,
    },
  },
  event: {
    wizzard: {
      step: {
        /*
        games: {
          preset: eventWizzardStepGamesPreset,
          list: eventWizzardStepGamesList,
          info: eventWizzardStepGameInfo,
        },
        review: {
          list: eventWizzardStepReviewList,
          info: eventWizzardStepReviewInfo,
        },
         */
      },
      settings: eventWizzardSettings,

      do: {
        /*
        updateGameStatus: eventWizzardUpdateGameStatus,
         */
        createPreset: eventWizzardCreatePreset,
        updateSettings: eventWizzardUpdateSettings,
        updateSettingsExtended: eventWizzardUpdateSettingsExtended,
      },
    },
    basic: eventBasic,
    extended: eventExtended,
    organizers: eventOrganizers,
    participant: {
      info: eventParticipant,
      list: eventParticipantList,
      // gamesList: eventGamesList,
      do: {
        updateStatus: changeUserEventStatus,
      },
    },
    community: {
      do: {
        invite: inviteCommunity,
        cancelInvite: cancelInviteCommunity,
      },
    },
    games: {
      list: eventGamesList,
      do: {
        updateStatus: updateGameChangeStatus,
      },
      /*
      do: {
        addGameWish: eventGamesAddGameWish, // add games from BGG (search for ANY game to add as wishlist). If only BGG - consider adding to DB(???) Limit to ~5 games per user? ~100 games per event?
        removeGameWish: eventGamesRemoveGameWish, // remove games from BGG
        addGameComment: eventGamesAddGameComment,
      },
       */
    },
    do: {
      update: configureEvent,
    },
  },
  community: {
    basic: communityBasic,
    expanded: communityExpanded2,
    games: {
      list: communityGamesList2,
      game: communityGameExtended2,
    },
    user: {
      list: communityUserList,
      info: communityUserInfo,
      do: {
        updateStatus: changeUserStatus,
        updateGameShare: switchGameShare,
      },
    },
    events: {
      invites: communityInvites,
      do: {
        create: createEvent,
        confirmInvite: confirmCommunityEventInvite,
        declineInvite: declineCommunityEventInvite,
      },
    },
    do: {
      update: configureCommunity,
      invite: createInvite,
    },
  },
  protected: {
    search: {
      communities: searchCommunities,
    },
    do: {
      createCommunity,
    },
    community: {
      list: communityList,
      publicList: publicCommunityList,
      do: {
        create: createCommunity,
        join: joinCommunity,
        inviteJoin: joinByInvite,
        inviteJoinPrejoin: joinByInvitePrejoin,
      },
    },
    game: {
      itemInfo: getItemInfo,
    },
    event: {
      list: eventsList,
      do: {
        deletePreset: protectedEventDeletePreset,
        create: createPrivateEvent,
      },
    },
    user: {
      info: getMyInfo,
      games: getMyGameInfo,
      do: {
        logout: logoutUser,
        updateProfile: userProfileUpdate,
        updateEventStatus: changeMyEventStatus,
        leaveCommunity,
      },
    },
  },
  public: {
    tags: {
      list: tagList,
    },
  },
}) // ...

export type AppRouter = typeof appRouter
