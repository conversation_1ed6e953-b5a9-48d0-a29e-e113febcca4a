import { and, eq, isNotNull } from "drizzle-orm"
import { undefined } from "zod"

import { BggGameDataExtracted } from "../../types/game.types"
import { db } from "../index"
import { gamesSchema } from "../schema/games.schema"
import { userToBaseGameSchema } from "../schema/userToBaseGame.schema"
import { userToGameToEventSchema } from "../schema/userToGameToEvent.schema"

export const getUserGames = async (userId: number, eventId?: number) => {
  let selectGames = db
    .select({
      id: gamesSchema.id,
      title: gamesSchema.title,
      bggId: gamesSchema.bggId,
      rating: userToBaseGameSchema.rating,
      lastPlay: userToBaseGameSchema.lastPlay,
      news: userToBaseGameSchema.news,
      playCount: userToBaseGameSchema.playCount,
      bggInfo: gamesSchema.bggInfo,
      lastUpdated: userToBaseGameSchema.lastUpdated,
      tags: userToBaseGameSchema.tags,
      ...(eventId
        ? {
            eventStatus: userToGameToEventSchema.status,
          }
        : {}),
    })
    .from(userToBaseGameSchema)

  if (eventId) {
    selectGames = selectGames.leftJoin(
      userToGameToEventSchema,
      and(
        eq(userToGameToEventSchema.userId, userId),
        eq(userToGameToEventSchema.gameId, gamesSchema.id),
        eq(userToGameToEventSchema.eventId, eventId),
      ),
    )
  }

  return await selectGames
    .where(
      and(
        eq(userToBaseGameSchema.userId, userId),
        isNotNull(gamesSchema.bggInfo),
      ),
    )
    .then(async (games) => {
      return await Promise.all(
        games.map(async (game) => {
          let bggDataExtracted = {} as unknown as BggGameDataExtracted
          try {
            bggDataExtracted = JSON.parse(
              game.bggInfo ?? "{}",
            ) as BggGameDataExtracted
          } catch (e: unknown) {
            console.error(
              "Error while parsing bgg info:",
              e,
              `Info: ${game.bggInfo}`,
              `Game id: ${game.id}`,
            )
          }

          let tags: number[] = []

          try {
            tags =
              game.tags && game.tags.length > 0
                ? (JSON.parse(game.tags ?? "[]") as number[])
                : []
          } catch (e: unknown) {
            console.error(
              "Error while parsing tags:",
              e,
              `Tags: ${game.tags}`,
              `Game id: ${game.id}`,
            )
          }

          let eventStatus:
            | "wish"
            | "willbring"
            | "canask"
            | "willnotbring"
            | "willplay"
            | null = null

          if (eventId) {
            eventStatus = await db
              .select({ status: userToGameToEventSchema.status })
              .from(userToGameToEventSchema)
              .where(
                and(
                  eq(userToGameToEventSchema.userId, userId),
                  eq(userToGameToEventSchema.gameId, game.id),
                  eq(userToGameToEventSchema.eventId, eventId),
                ),
              )
              .then((status) => status[0]?.status ?? null)
          }

          return {
            ...game,
            tags,
            bggInfo: undefined,
            eventStatus,
            players: {
              box: {
                min: parseInt(bggDataExtracted.players?.box?.min ?? "0"),
                max: parseInt(bggDataExtracted.players?.box?.max ?? "0"),
              },
              stats: bggDataExtracted.players?.stats?.map((stat) => [
                parseInt(stat?.players ?? "0"),
                stat?.status ?? 1,
              ]),
            },
            weight: parseInt(bggDataExtracted.averageweight ?? "0"),
            average:
              Math.round(parseFloat(bggDataExtracted.average ?? "0") * 100) /
              100,
          }
        }),
      )
    })
}
