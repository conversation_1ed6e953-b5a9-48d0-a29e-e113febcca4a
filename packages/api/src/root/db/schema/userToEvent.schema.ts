import { boolean, int, mysqlEnum, mysqlTable } from "drizzle-orm/mysql-core"

import { commonCreated } from "./common"

export const userToEventSchema = mysqlTable("user2event", {
  ...commonCreated,
  userId: int("user_id").notNull(),
  eventId: int("event_id").notNull(),
  shareGames: boolean("share_games"),
  wizzardState: mysqlEnum("wizzard_state", [
    "skip",
    "waiting",
    "settings",
    "pick-games",
    "review-games",
    "done",
  ]),
  canRequest: boolean("can_request").notNull(),
})
