import {
  boolean,
  float,
  int,
  mysqlEnum,
  mysqlTable,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core"

import { commonCreated, commonId } from "./common"

export const eventSchema = mysqlTable("events", {
  ...commonId,
  title: varchar({ length: 256 }).notNull(),
  openness: mysqlEnum([
    "public",
    "publicLimited",
    "community",
    "seniority",
    "private",
    "closed",
  ]).notNull(),
  creatorId: int("creator_id"),
  image: varchar({ length: 256 }).notNull(),
  memberApproval: boolean("member_approval").notNull(),
  location: varchar("location", { length: 256 }),
  description: text("description"),
  starts: timestamp("starts").notNull(),
  ends: timestamp("ends"),
  maxCapacity: int("max_capacity"),
  reserveCapacity: int("reserve_capacity"),
  going: int("going").notNull(),
  reserve: int("reserve"),
  share: mysqlEnum("share_type", [
    "none", // nobody cab share
    "all", // anybody can share
    "hosts", // only hosts can share
    "host-managed", // hosts can set who can share
    "single-game", // one preset game (maybe same as "none")
  ]).notNull(),
  state: mysqlEnum([
    "open",
    "ongoing",
    "ended",
    "hidden",
    "cancelled",
  ]).notNull(),
  allowInfiniteOrganizers: boolean("allow_infinite_organizers").notNull(),
  smallDescription: text("small_description"),
  minCapacity: int("min_capacity"),
  hasAgeLimit: boolean("has_age_limit").notNull(),
  lat: float("lat"),
  lng: float("lng"),
  /*
  lowerAgeLimit: mysqlEnum("age_limit", [
    "none",
    "teen",
    "adult",
    "elder",
  ]).notNull(),
  higherAgeLimit: mysqlEnum("top_age_limit", [
    "none",
    "children",
    "teen",
    "adult",
    "elder",
  ]).notNull(),
   */
  ...commonCreated,
})
