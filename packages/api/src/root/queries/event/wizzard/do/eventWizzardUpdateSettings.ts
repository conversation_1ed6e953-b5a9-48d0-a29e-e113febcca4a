import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../../db"
import { userToEventSchema } from "../../../../db/schema/userToEvent.schema"
import { eventProcedure } from "../../../../trpc/procedures/eventProcedure"

export const eventWizzardUpdateSettings = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      share: z.boolean(),
      canRequest: z.boolean(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    await db
      .update(userToEventSchema)
      .set({
        shareGames: input.share,
        canRequest: input.canRequest,
      })
      .where(
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, event.id),
        ),
      )
      .then((re) => re)
  })
