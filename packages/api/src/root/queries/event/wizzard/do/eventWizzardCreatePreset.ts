import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../../db"
import { eventListPreset } from "../../../../db/schema/eventListPreset"
import { eventToGameSchema } from "../../../../db/schema/eventToGame.schema"
import { userToGameToEventSchema } from "../../../../db/schema/userToGameToEvent.schema"
import { eventProcedure } from "../../../../trpc/procedures/eventProcedure"

export const eventWizzardCreatePreset = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      title: z.string(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    const userEventGames = await Promise.all([
      db
        .select({
          id: userToGameToEventSchema.gameId,
          status: userToGameToEventSchema.status,
        })
        .from(userToGameToEventSchema)
        .where(
          and(
            eq(userToGameToEventSchema.eventId, event.id),
            eq(userToGameToEventSchema.userId, loginData.id),
          ),
        ),
    ])

    await db.insert(eventListPreset).values({
      userId: loginData.id,
      title: input.title,
      games: JSON.stringify(userEventGames),
    })
  })
