import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../../db"
import { eventListPreset } from "../../../../db/schema/eventListPreset"
import { userToEventSchema } from "../../../../db/schema/userToEvent.schema"
import { eventProcedure } from "../../../../trpc/procedures/eventProcedure"
import { EventPresetGame } from "../../../../types/game.types"
import { setGameState } from "../../helpers/setGameState"

export const eventWizzardUpdateSettingsExtended = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      presetId: z.number().nullable().optional(),
      share: z.boolean().optional(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    if (input.presetId) {
      const preset = await db
        .select({
          games: eventListPreset.games,
        })
        .from(eventListPreset)
        .where(
          and(
            eq(eventListPreset.userId, loginData.id),
            eq(eventListPreset.id, input.presetId),
          ),
        )
        .then((preset) => preset[0])

      if (!preset) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Preset not found",
        })
      }

      const games: EventPresetGame[] = JSON.parse(preset.games ?? "[]")

      await Promise.all(
        games.map(async (game) => {
          await setGameState({
            userId: loginData.id,
            eventId: event.id,
            gameId: game.id,
            status: game.status,
          })
        }),
      )
    }

    await db
      .update(userToEventSchema)
      .set({
        wizzardState: !input.share
          ? "done"
          : input.presetId
            ? "review-games"
            : "pick-games",
      })
      .where(
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, event.id),
        ),
      )
      .then((re) => re)
  })
