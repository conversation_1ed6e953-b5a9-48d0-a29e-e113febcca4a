import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { db } from "../../../db"
import { getUserGames } from "../../../db/queries/getUserGames"
import { tagsAndCats } from "../../../db/queries/tagsAndCats"
import { eventSchema } from "../../../db/schema/event.schema"
import { eventListPreset } from "../../../db/schema/eventListPreset"
import { userToEventSchema } from "../../../db/schema/userToEvent.schema"
import { eventProcedure } from "../../../trpc/procedures/eventProcedure"

export const eventWizzardSettings = eventProcedure
  .input(z.object({ eventId: z.number() }))
  .query(async ({ input, ctx: { loginData, event } }) => {
    if (!hasPermission(loginData, "event", "shareUserGames", event)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't share games",
      })
    }

    const presets = await db
      .select({
        id: eventListPreset.id,
        title: eventListPreset.title,
      })
      .from(eventListPreset)
      .where(eq(eventListPreset.userId, loginData.id))
      .then((presets) => presets)

    const games = await getUserGames(loginData.id, event.id)

    const tags = await tagsAndCats()

    const settings = await db
      .select({
        wizzardState: userToEventSchema.wizzardState,
        share: userToEventSchema.shareGames,
        canRequest: userToEventSchema.canRequest,
      })
      .from(userToEventSchema)
      .innerJoin(eventSchema, eq(userToEventSchema.eventId, eventSchema.id))
      .where(
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, input.eventId),
        ),
      )
      .then((settings) => settings[0])

    if (!settings) {
      await db
        .insert(userToEventSchema)
        .values({
          userId: loginData.id,
          eventId: input.eventId,
          shareGames: false,
          wizzardState: "waiting",
          canRequest: true,
        })
        .then((re) => re)

      const response: {
        wizzardState: "waiting"
        share: boolean
        presets: typeof presets
        games: typeof games
        canRequest: boolean
      } & typeof tags = {
        wizzardState: "waiting",
        share: false,
        canRequest: true,
        games,
        presets,
        ...tags,
      }

      return response
    }

    return { ...settings, presets, games, ...tags }
  })
