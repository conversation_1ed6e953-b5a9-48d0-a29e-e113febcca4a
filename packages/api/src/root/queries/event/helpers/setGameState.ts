import { and, eq } from "drizzle-orm"

import { db } from "../../../db"
import { eventToGameSchema } from "../../../db/schema/eventToGame.schema"
import { userToGameToEventSchema } from "../../../db/schema/userToGameToEvent.schema"

type SetGameStateProps = {
  userId: number
  eventId: number
  gameId: number
  status: "wish" | "willbring" | "canask" | "willnotbring" | "willplay"
}
export const setGameState = async ({
  userId,
  eventId,
  gameId,
  status,
}: SetGameStateProps) => {
  const existsEntry = await db
    .select({ id: eventToGameSchema.id })
    .from(eventToGameSchema)
    .where(
      and(
        eq(eventToGameSchema.eventId, eventId),
        eq(eventToGameSchema.gameId, gameId),
      ),
    )
    .then((res) => res.length > 0)

  if (!existsEntry) {
    await db
      .insert(eventToGameSchema)
      .values({
        eventId,
        gameId,
      })
      .then((re) => re)
  }

  await db
    .insert(userToGameToEventSchema)
    .values({
      eventId,
      gameId,
      userId,
      status,
    })
    .onDuplicateKeyUpdate({ set: { status } })
    .then((re) => re)
}
