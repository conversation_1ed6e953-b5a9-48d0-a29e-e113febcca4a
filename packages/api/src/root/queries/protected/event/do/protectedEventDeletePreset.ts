import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../../db"
import { eventListPreset } from "../../../../db/schema/eventListPreset"
import { protectedProcedure } from "../../../../trpc/procedures/protectedProcedure"

export const protectedEventDeletePreset = protectedProcedure
  .input(
    z.object({
      eventId: z.number(),
      presetId: z.number(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData } }) => {
    await db
      .delete(eventListPreset)
      .where(
        and(
          eq(eventListPreset.userId, loginData.id),
          eq(eventListPreset.id, input.presetId),
        ),
      )
      .then((re) => re)
  })
