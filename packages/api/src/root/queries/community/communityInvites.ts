import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"
import { db } from "../../db"
import { communityToEventSchema } from "../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../db/schema/event.schema"
import { usersSchema } from "../../db/schema/users.schema"
import { selectEvents } from "../../db/select/select.events"
import { communityProcedure } from "../../trpc/procedures/communityProcedure"
import { hostInfoSchema } from "../event/eventsListHelper"
import { getEventCommunities } from "../event/helpers/getEventHosts"

export const communityInvites = communityProcedure
  .input(z.object({ communityId: z.number() }))
  .query(async ({ ctx: { loginData, community } }) => {
    if (
      !hasPermission(loginData, "community", "event", {
        id: community.id,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to view invites",
      })
    }

    if (
      !hasPermission(loginData, "community", "update", {
        id: community.id,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to view invites",
      })
    }

    return await db
      .select({ ...selectEvents })
      .from(communityToEventSchema)
      .innerJoin(
        eventSchema,
        eq(eventSchema.id, communityToEventSchema.eventId),
      )
      .leftJoin(
        hostInfoSchema,
        and(
          eq(hostInfoSchema.subject, "event"),
          eq(hostInfoSchema.roleId, "host"),
          eq(hostInfoSchema.subjectId, selectEvents.id),
        ),
      )
      .leftJoin(usersSchema, eq(hostInfoSchema.userId, usersSchema.id))
      .where(
        and(
          eq(communityToEventSchema.communityId, community.id),
          eq(communityToEventSchema.invite, true),
        ),
      )
      .then((invites) =>
        Promise.all(
          invites.map(async (invite) => ({
            ...invite,
            communities: await getEventCommunities({
              eventId: invite.id,
              userCommunities: [],
            }),
          })),
        ),
      )
  })
