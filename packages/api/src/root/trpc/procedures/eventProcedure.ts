import { TRPCError } from "@trpc/server"
import { eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"
import { db } from "../../db"
import { communityToEventSchema } from "../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../db/schema/event.schema"
import { checkLogin } from "../checkLogin"
import { t } from "../trpc"

export const eventProcedure = t.procedure
  .input(z.object({ eventId: z.number() }))
  .use(async function isAuthed(opts) {
    if (!opts.ctx.auth) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : You are not logged in",
      })
    }
    const loginData = await checkLogin(opts.ctx?.auth?.payload?.sub)

    const { eventId } = opts.input

    if (!eventId) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Unauthorized: : No Event Selected",
      })
    }

    const event = await db
      .select({
        id: eventSchema.id,
        openness: eventSchema.openness,
        status: eventSchema.state,
        memberApproval: eventSchema.memberApproval,
        share: eventSchema.share,
        allowInfiniteOrganizers: eventSchema.allowInfiniteOrganizers,
      })
      .from(eventSchema)
      .where(eq(eventSchema.id, eventId))
      .then(async (event) => {
        if (!event[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Unauthorized: : Such event does not exist",
          })
        }

        const communities = await db
          .select({
            id: communityToEventSchema.communityId,
            owner: communityToEventSchema.owner,
          })
          .from(communityToEventSchema)
          .where(eq(communityToEventSchema.eventId, event[0].id))
          .then((communities) => communities)

        return { ...event[0], communities, hosts: communities }
      })

    if (!event) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Unauthorized: : Such event does not exist",
      })
    }

    if (
      !hasPermission(loginData, "event", "view", event) ||
      !hasPermission(loginData, "event", "viewPublic", event)
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Forbidden: : You have no access to this event",
      })
    }

    return opts.next({
      ctx: {
        // Infers the `session` as non-nullable
        auth: opts.ctx.auth,
        loginData,
        event,
      },
    })
  })
