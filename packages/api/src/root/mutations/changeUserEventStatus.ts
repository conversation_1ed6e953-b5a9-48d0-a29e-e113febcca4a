import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../common/src/permissions/hasPermissions"
import { Role } from "../../../../common/src/permissions/roles/helpers/types"
import { db } from "../db"
import { getUser } from "../db/queries/getUser"
import { eventSchema } from "../db/schema/event.schema"
import { userToEventSchema } from "../db/schema/userToEvent.schema"
import { usersSchema } from "../db/schema/users.schema"
import { selectEventData } from "../db/select/select.eventData"
import {
  removeEventRole,
  setAsCohost,
  setAsInterested,
  setAsParticipant,
  setAsRequested,
  setAsReserved,
  setAsUnwelcome,
} from "../helpers/insertEventRoleAndCount"
import { removeRoles } from "../helpers/removeRoles"
import { OK_RESPONSE } from "../helpers/responses"
import { hostInfoSchema } from "../queries/event/eventsListHelper"
import { eventProcedure } from "../trpc/procedures/eventProcedure"

export const changeUserEventStatus = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      userId: z.number(),
      removeRole: z.boolean().optional(),
      status: z.enum([
        "participant",
        "interested",
        "requested",
        "reserved",
        "notgoing",
        "unwelcome",
        "cohost",
        "host",
      ]),
    }),
  )
  .mutation(
    async ({
      input: { eventId, status, userId, removeRole },
      ctx: { loginData },
    }) => {
      if (!hasPermission(loginData, "event", "approve", { id: eventId })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to approve new participants",
        })
      }

      const user = await getUser(userId)

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Not Found: Such user does not exist",
        })
      }

      const event = await db
        .select({ ...selectEventData })
        .from(eventSchema)
        .leftJoin(
          hostInfoSchema,
          and(
            eq(hostInfoSchema.subject, "event"),
            eq(hostInfoSchema.roleId, "host"),
            eq(hostInfoSchema.subjectId, selectEventData.id),
          ),
        )
        .leftJoin(usersSchema, eq(hostInfoSchema.userId, usersSchema.id))
        .where(eq(eventSchema.id, eventId))
        .then(async (event) => event[0])

      if (!event) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Not Found: : Such event does not exist",
        })
      }

      const isHost = hasPermission(user, "event", "isHost", { id: eventId })

      if (isHost) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You can't change host status",
        })
      }

      let requestResult: Role | null = null

      if (removeRole) {
        if (status === "host") {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You can't remove cohost status",
          })
        }

        if (
          status === "cohost" &&
          !hasPermission(loginData, "event", "promoteCohost", { id: eventId })
        ) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You can't remove cohost status",
          })
        }

        await removeRoles(user.id, "event", eventId, status as Role)
        return { ...OK_RESPONSE, role: requestResult }
      }

      try {
        switch (status) {
          case "requested":
            requestResult = await setAsRequested({
              user: user,
              event,
              force: true,
            })
            break
          case "reserved":
            requestResult = await setAsReserved({
              user: user,
              event,
              force: true,
            })
            break
          case "participant":
            requestResult = await setAsParticipant({
              user: user,
              event,
              force: true,
              approveRole: true,
            })
            break
          case "interested":
            requestResult = await setAsInterested({
              user: user,
              event,
              force: true,
            })
            break
          case "cohost":
            requestResult = await setAsCohost({
              user: user,
              event,
            })
            break
          case "unwelcome":
            requestResult = await setAsUnwelcome({
              user: user,
              event,
              force: true,
            })
            break
          case "notgoing":
          default:
            requestResult = await removeEventRole({
              user: user,
              event,
            })
            await db
              .delete(userToEventSchema)
              .where(
                and(
                  eq(userToEventSchema.userId, loginData.id),
                  eq(userToEventSchema.eventId, eventId),
                ),
              )
              .then((re) => re)
            break
        }
      } catch (error: unknown) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error as string,
        })
      }

      return { ...OK_RESPONSE, role: requestResult }
    },
  )
