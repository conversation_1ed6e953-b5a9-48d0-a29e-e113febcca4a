import { TRPCError } from "@trpc/server"
import { and, eq, lte, or } from "drizzle-orm"
import { z } from "zod"

import { db } from "../db"
import { communitySchema } from "../db/schema/community.schema"
import { communityInvitesSchema } from "../db/schema/communityInvites.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { userToCommunitySchema } from "../db/schema/userToCommunity.schema"
import { OK_RESPONSE } from "../helpers/responses"
import { protectedProcedure } from "../trpc/procedures/protectedProcedure"

export const joinCommunity = protectedProcedure
  .input(
    z.object({
      communityId: z.number(),
      inviteString: z.string().optional(),
      shareMyGames: z.boolean().optional(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData } }) => {
    const communityCheck = await db
      .select({
        id: communitySchema.id,
        openness: communitySchema.openness,
        approval: communitySchema.memberApproval,
        share: communitySchema.allowShare,
      })
      .from(communitySchema)
      .where(eq(communitySchema.id, input.communityId))
      .then((community) => community[0])

    if (!communityCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "This community does not exist",
      })
    }

    const invite =
      communityCheck.openness === "public" ||
      (await db
        .select({
          id: communityInvitesSchema.id,
          expiration: communityInvitesSchema.expiration,
          status: communityInvitesSchema.status,
          acceptLimit: communityInvitesSchema.acceptLimit,
          count: communityInvitesSchema.count,
          accepted: communityInvitesSchema.accepted,
        })
        .from(communityInvitesSchema)
        .where(
          and(
            eq(communityInvitesSchema.communityId, input.communityId),
            lte(communityInvitesSchema.expiration, new Date()),
            or(
              eq(communityInvitesSchema.userId, loginData.id),
              eq(communityInvitesSchema.inviteString, input.inviteString ?? ""),
            ),
          ),
        )
        .then((invite) => invite[0]))

    if (!invite) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Invite required to join this community",
      })
    }

    if (
      // do not simplify invite !== true as it will break code (need boolean check)
      invite !== true &&
      (invite.status !== "sent" ||
        (invite.acceptLimit !== null &&
          invite.acceptLimit <= (invite.count ?? 0)))
    ) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Valid invite required to join this community",
      })
    }

    const connectionCheck = await db
      .select({ communityId: userToCommunitySchema.communityId })
      .from(userToCommunitySchema)
      .where(
        and(
          eq(userToCommunitySchema.userId, loginData.id),
          eq(userToCommunitySchema.communityId, input.communityId),
        ),
      )
      .then((communityId) => communityId[0])

    if (connectionCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "User already has joined this community",
      })
    }

    db.insert(userToCommunitySchema)
      .values({
        userId: loginData.id,
        communityId: communityCheck.id,
        trust: 0,
        shareMyGames: communityCheck.share ? false : !!input.shareMyGames,
      })
      .then((re) => re)

    await db.insert(permissionUserToRoleSchema).values({
      userId: loginData.id,
      roleId: communityCheck.approval ? "invited" : "member",
      subject: "community",
      subjectId: communityCheck.id,
    })

    // do not simplify invite !== true as it will break code (need boolean check)
    if (invite && invite !== true) {
      const inviteUpdate = { ...invite, id: undefined }

      if (invite.acceptLimit) {
        inviteUpdate.count = (invite.count ?? 0) + 1
        if (inviteUpdate.count >= invite.acceptLimit) {
          inviteUpdate.status = "used"
        }
      } else {
        inviteUpdate.status = "used"
        inviteUpdate.accepted = new Date()
      }

      db.update(communityInvitesSchema)
        .set(inviteUpdate)
        .where(eq(communityInvitesSchema.id, invite.id ?? 0))
        .then((re) => re)
    }

    return OK_RESPONSE
  })
