import { TRPCError } from "@trpc/server"
import { and, count, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../common/src/permissions/hasPermissions"
import { db } from "../db"
import { communityToEventSchema } from "../db/schema/communityToEvent.schema"
import { OK_RESPONSE } from "../helpers/responses"
import { eventProcedure } from "../trpc/procedures/eventProcedure"

const MAX_ORGANIZERS = 5

export const inviteCommunity = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      communityId: z.number(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    if (
      !hasPermission(loginData, "event", "update", {
        id: input.eventId,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to invite community",
      })
    }

    if (event.openness === "private" || event.openness === "closed") {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Event is not open for community invitations",
      })
    }

    if (!event.allowInfiniteOrganizers) {
      const organizerCount = await db
        .select({ count: count() })
        .from(communityToEventSchema)
        .where(and(eq(communityToEventSchema.eventId, input.eventId)))
        .then((count) => count[0].count)

      if (organizerCount >= MAX_ORGANIZERS) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Too many communities",
        })
      }
    }

    const communityCheck = await db
      .select({ id: communityToEventSchema.eventId })
      .from(communityToEventSchema)
      .where(
        and(
          eq(communityToEventSchema.eventId, input.eventId),
          eq(communityToEventSchema.communityId, input.communityId),
        ),
      )
      .then((community) => community[0])

    if (communityCheck) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Community already invited",
      })
    }

    await db
      .insert(communityToEventSchema)
      .values({
        eventId: input.eventId,
        communityId: input.communityId,
        owner: false,
        invite: true,
      })
      .then((re) => re)

    return OK_RESPONSE
  })
