import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../common/src/permissions/hasPermissions"
import { db } from "../db"
import { communityToEventSchema } from "../db/schema/communityToEvent.schema"
import { OK_RESPONSE } from "../helpers/responses"
import { eventProcedure } from "../trpc/procedures/eventProcedure"

export const cancelInviteCommunity = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      communityId: z.number(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData } }) => {
    if (
      !hasPermission(loginData, "event", "update", {
        id: input.eventId,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to cancel community invite",
      })
    }

    const communityCheck = await db
      .select({ id: communityToEventSchema.eventId })
      .from(communityToEventSchema)
      .where(
        and(
          eq(communityToEventSchema.eventId, input.eventId),
          eq(communityToEventSchema.communityId, input.communityId),
        ),
      )
      .then((community) => community[0])

    if (!communityCheck) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Community not invited",
      })
    }

    await db
      .delete(communityToEventSchema)
      .where(
        and(
          eq(communityToEventSchema.eventId, input.eventId),
          eq(communityToEventSchema.communityId, input.communityId),
        ),
      )
      .then((re) => re)

    return OK_RESPONSE
  })
