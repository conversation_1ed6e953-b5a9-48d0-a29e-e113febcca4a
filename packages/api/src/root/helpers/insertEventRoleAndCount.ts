import { TRPCError } from "@trpc/server"
import { and, count, eq } from "drizzle-orm"

import { hasPermission } from "../../../../common/src/permissions/hasPermissions"
import { Event } from "../../../../common/src/permissions/objects/event"
import {
  Role,
  RoleData,
} from "../../../../common/src/permissions/roles/helpers/types"
import { db } from "../db"
import { eventSchema } from "../db/schema/event.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { userToEventSchema } from "../db/schema/userToEvent.schema"

import { removeRoles } from "./removeRoles"

export const countRole = async ({
  roleId,
  eventId,
}: {
  roleId: Role
  eventId: number
}) => {
  const fields = roleId === "participant" ? "going" : "reserve"

  const countTotal = await db
    .select({ count: count() })
    .from(permissionUserToRoleSchema)
    .where(
      and(
        eq(permissionUserToRoleSchema.subject, "event"),
        eq(permissionUserToRoleSchema.subjectId, eventId),
        eq(
          permissionUserToRoleSchema.roleId,
          roleId === "participant" ? "participant" : "reserved",
        ),
      ),
    )
    .then((count) => count[0].count)

  await db
    .update(eventSchema)
    .set({
      [fields]: countTotal,
    })
    .where(eq(eventSchema.id, eventId))
}

const createEventSettings = async (
  userId: number,
  event: Pick<Event, "id" | "share">,
) => {
  const wizzardState = (event.share ?? "none") === "all" ? "waiting" : "skip"

  await db
    .insert(userToEventSchema)
    .values({
      userId: userId,
      eventId: event.id!,
      shareGames: false,
      canRequest: true,
      wizzardState,
    })
    .onDuplicateKeyUpdate({ set: { shareGames: false, wizzardState } })
}

const countRoles = async (eventId: number) => {
  await countRole({ roleId: "participant", eventId })
  await countRole({ roleId: "reserved", eventId })
}

export const insertEventRoleAndCount = async (
  userId: number,
  eventId: number,
  roleId:
    | "host"
    | "cohost"
    | "participant"
    | "interested"
    | "requested"
    | "reserved"
    | "unwelcome",
) => {
  const checkForDuplicate = await db
    .select({ id: permissionUserToRoleSchema.id })
    .from(permissionUserToRoleSchema)
    .where(
      and(
        eq(permissionUserToRoleSchema.userId, userId),
        eq(permissionUserToRoleSchema.subject, "event"),
        eq(permissionUserToRoleSchema.subjectId, eventId),
        eq(permissionUserToRoleSchema.roleId, roleId),
      ),
    )
    .then((res) => res.length > 0)

  if (checkForDuplicate) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "User already has this role",
    })
  }

  await db.insert(permissionUserToRoleSchema).values({
    userId,
    roleId,
    subject: "event",
    subjectId: eventId,
  })

  await countRoles(eventId)
}

type RoleSetProps = {
  user: { id: number; roles: RoleData[] }
  event: Event & { memberApproval: boolean }
  force?: boolean
  approveRole?: boolean
}

export const setAsParticipant = async ({
  user,
  event,
  force = false,
  approveRole = false,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const canJoin = force || hasPermission(user, "event", "join", event)

  if (!canJoin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to join this event",
    })
  }

  if (event.memberApproval && !approveRole && !force) {
    return setAsRequested({ user, event, force, approveRole })
  }

  if (
    !force &&
    (event.maxCapacity ?? 0) > 0 &&
    (event.going ?? 0) >= (event.maxCapacity ?? 0)
  ) {
    return setAsReserved({ user, event })
  }

  await removeRoles(user.id, "event", event.id)

  await insertEventRoleAndCount(user.id, event.id, "participant")

  await createEventSettings(user.id, event)

  return "participant"
}

export const setAsRequested = async ({
  user,
  event,
  force = false,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const canJoin = force || hasPermission(user, "event", "join", event)

  if (!canJoin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to join this event",
    })
  }

  const isRequested = hasPermission(user, "event", "isRequested", event)

  if (isRequested) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You have already requested to join this event",
    })
  }

  const isWaiting = hasPermission(user, "event", "isWaiting", event)

  if (isWaiting) {
    await removeRoles(user.id, "event", event.id)
  }

  await insertEventRoleAndCount(user.id, event.id, "requested")
  return "requested"
}

export const setAsReserved = async ({
  user,
  event,
  force,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const canJoin = force || hasPermission(user, "event", "join", event)

  if (!canJoin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to join this event",
    })
  }

  if (!event.reserveCapacity || (event.reserve ?? 0) >= event.reserveCapacity) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "Event is full",
    })
  }

  const isWaiting = hasPermission(user, "event", "isWaiting", event)

  if (isWaiting) {
    await removeRoles(user.id, "event", event.id)
  }

  await insertEventRoleAndCount(user.id, event.id, "reserved")
  return "reserved"
}

export const setAsInterested = async ({
  user,
  event,
  force,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const canJoin = force || hasPermission(user, "event", "join", event)

  if (!canJoin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to join this event",
    })
  }

  const isWaiting = hasPermission(user, "event", "isWaiting", event)
  const isParticipant = hasPermission(user, "event", "isParticipant", event)

  if (isParticipant || isWaiting) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You are already participant of this event",
    })
  }

  await insertEventRoleAndCount(user.id, event.id, "interested")
  return "interested"
}

export const setAsUnwelcome = async ({
  user,
  event,
  force,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  if (!force) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to block participants",
    })
  }

  await removeRoles(user.id, "event", event.id)
  await insertEventRoleAndCount(user.id, event.id, "unwelcome")
  return "unwelcome"
}

export const setAsCohost = async ({
  user,
  event,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const isWaiting = hasPermission(user, "event", "isWaiting", event)

  if (isWaiting) {
    await removeRoles(user.id, "event", event.id)
  }

  const isParticipant = hasPermission(user, "event", "isParticipant", event)

  if (!isParticipant) {
    await insertEventRoleAndCount(user.id, event.id, "participant")
    await createEventSettings(user.id, event)
  }

  await insertEventRoleAndCount(user.id, event.id, "cohost")

  return "cohost"
}

export const removeEventRole = async ({
  user,
  event,
}: RoleSetProps): Promise<null> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const isDenied = hasPermission(user, "event", "isDenied", event)
  if (isDenied) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  await removeRoles(user.id, "event", event.id)
  return null
}
